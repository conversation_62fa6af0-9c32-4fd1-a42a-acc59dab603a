13:53:54.721 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.0.21.Final
13:53:54.743 [restartedMain] INFO  c.m.LawcaseWxApplication - [logStarting,55] - Starting LawcaseWxApplication on lldeMacBook-Pro.local with PID 16198 (/Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus-ord/lawcase-admin/target/classes started by ll in /Users/<USER>/Desktop/ww/jj/jeeplus-parent/jeeplus-ord/lawcase-admin)
13:53:54.743 [restartedMain] INFO  c.m.LawcaseWxApplication - [logStartupProfileInfo,655] - The following profiles are active: druid
13:53:56.721 [restartedMain] INFO  o.e.jetty.util.log - [initialized,169] - Logging initialized @2766ms to org.eclipse.jetty.util.log.Slf4jLog
13:53:56.975 [restartedMain] INFO  o.e.j.server.Server - [doStart,375] - jetty-9.4.35.v20201120; built: 2020-11-20T21:17:03.964Z; git: bdc54f03a5e0a7e280fab27f55c3c75ee8da89fb; jvm 1.8.0_432-b06
13:53:57.004 [restartedMain] INFO  o.e.j.s.h.C.application - [log,2363] - Initializing Spring embedded WebApplicationContext
13:53:57.635 [restartedMain] INFO  o.e.j.server.session - [doStart,334] - DefaultSessionIdManager workerName=node0
13:53:57.636 [restartedMain] INFO  o.e.j.server.session - [doStart,339] - No SessionScavenger set, using defaults
13:53:57.637 [restartedMain] INFO  o.e.j.server.session - [startScavenging,132] - node0 Scavenging every 600000ms
13:53:57.651 [restartedMain] INFO  o.e.j.s.h.ContextHandler - [doStart,916] - Started o.s.b.w.e.j.JettyEmbeddedWebAppContext@34cff055{application,/,[file:///private/var/folders/8_/k38v677d3xj_528wsn350zpr0000gn/T/jetty-docbase.7500.7545614196144722070/, jar:file:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-ui/2.9.2/springfox-swagger-ui-2.9.2.jar!/META-INF/resources],AVAILABLE}
13:53:57.651 [restartedMain] INFO  o.e.j.server.Server - [doStart,415] - Started @3697ms
13:53:59.668 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,985] - {dataSource-1} inited
13:54:00.028 [restartedMain] INFO  i.l.c.EpollProvider - [<clinit>,68] - Starting without optional epoll library
13:54:00.030 [restartedMain] INFO  i.l.c.KqueueProvider - [<clinit>,70] - Starting without optional kqueue library
13:54:00.272 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2032] - {dataSource-1} closing ...
13:54:00.278 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2104] - {dataSource-1} closed
13:54:00.402 [restartedMain] INFO  o.e.j.server.session - [stopScavenging,149] - node0 Stopped scavenging
13:54:00.405 [restartedMain] INFO  o.e.j.s.h.ContextHandler - [doStop,1154] - Stopped o.s.b.w.e.j.JettyEmbeddedWebAppContext@34cff055{application,/,[file:///private/var/folders/8_/k38v677d3xj_528wsn350zpr0000gn/T/jetty-docbase.7500.7545614196144722070/, jar:file:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-ui/2.9.2/springfox-swagger-ui-2.9.2.jar!/META-INF/resources],STOPPED}
